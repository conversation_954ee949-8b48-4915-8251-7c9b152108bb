<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main">
    <template #before></template>

    <template #toolbar:after>
      <el-button type="primary" @click="handleCreateTask">
        <i class="el-icon-plus"></i>
        创建新任务
      </el-button>
      <el-button type="success" @click="handleReExtract">
        <i class="el-icon-refresh"></i>
        重新萃取
      </el-button>
    </template>

    <!-- 萃取状态列自定义渲染 -->
    <template #table:extractionStatus:simple="{ row }">
      <el-tag :type="getExtractionStatusType(row.extractionStatus)">
        {{ row.extractionStatus }}
      </el-tag>
    </template>

    <!-- 文件格式列自定义渲染 -->
    <template #table:fileFormat:simple="{ row }">
      <el-tag type="primary">
        {{ row.fileFormat }}
      </el-tag>
    </template>

    <!-- 文件状态列自定义渲染 -->
    <template #table:fileStatus:simple="{ row }">
      <el-tag :type="getFileStatusType(row.fileStatus)">
        {{ row.fileStatus }}
      </el-tag>
    </template>

    <!-- 操作列自定义渲染 -->
    <template #table:action:after="{ row }">
      <el-button type="text" size="small" @click="handleViewDetails(row)">
        查看详情
      </el-button>
    </template>

    <template #info:before></template>
    <template #after></template>
  </EleSheet>
</template>

<script>
import request from '@/utils/request.js'
import { fileFormat, fileStatus, extractionStatus } from '@/dicts/video/index.js'

export default {
  name: 'VideoProcessTemplate',
  data() {
    return {
      tableType: 'video_process_template'
    }
  },
  computed: {
    sheetProps() {
      return {
        title: '视频处理模板',
        api: {
          list: (params) =>
            request({
              url: '/system/AutoOsmotic/list',
              method: 'get',
              params: {
                ...params,
                type: this.tableType
              }
            }),
          info: (id) =>
            request({
              url: `/system/AutoOsmotic/${id}`,
              method: 'get'
            }),
          add: (data) =>
            request({
              url: '/system/AutoOsmotic',
              method: 'post',
              data: {
                ...data,
                type: this.tableType
              }
            }),
          edit: (data) =>
            request({
              url: '/system/AutoOsmotic',
              method: 'put',
              data: {
                ...data,
                type: this.tableType
              }
            }),
          remove: (ids) =>
            request({
              url: `/system/AutoOsmotic/${ids}`,
              method: 'delete'
            })
        },
        model: {
          id: {
            type: 'text',
            label: '视频文件ID',
            width: 120,
            fixed: 'left',
            search: { hidden: true },
            form: { hidden: true }
          },
          taskNumber: {
            type: 'text',
            label: '任务编号',
            width: 150,
            search: { hidden: true },
            form: {
              type: 'input',
              rules: [
                { required: true, message: '请输入任务编号', trigger: 'blur' }
              ]
            }
          },
          fileName: {
            type: 'text',
            label: '视频文件名',
            width: 200,
            showOverflowTooltip: true,
            search: {
              type: 'input',
              placeholder: '请输入文件名'
            },
            form: {
              type: 'input',
              rules: [
                { required: true, message: '请输入视频文件名', trigger: 'blur' }
              ]
            }
          },
          fileSize: {
            type: 'text',
            label: '文件大小',
            width: 120,
            search: { hidden: true },
            form: {
              type: 'input'
            }
          },
          removeBlackScreen: {
            type: 'text',
            label: '剔除黑屏',
            width: 100,
            search: { hidden: true },
            form: {
              type: 'switch',
              props: {
                activeText: '是',
                inactiveText: '否'
              }
            }
          },
          removeFlowerScreen: {
            type: 'text',
            label: '剔除花屏',
            width: 100,
            search: { hidden: true },
            form: {
              type: 'switch',
              props: {
                activeText: '是',
                inactiveText: '否'
              }
            }
          },
          processedFileName: {
            type: 'text',
            label: '剔除后视频文件名',
            width: 200,
            showOverflowTooltip: true,
            search: { hidden: true },
            form: {
              type: 'input'
            }
          },
          extractionTime: {
            type: 'text',
            label: '萃取时间',
            width: 160,
            search: { hidden: true },
            form: {
              type: 'date-picker',
              props: {
                type: 'datetime',
                placeholder: '请选择萃取时间',
                format: 'yyyy-MM-dd HH:mm:ss',
                valueFormat: 'yyyy-MM-dd HH:mm:ss'
              }
            }
          },
          extractionStatus: {
            type: 'text',
            label: '萃取状态',
            width: 100,
            search: {
              type: 'select',
              placeholder: '请选择萃取状态',
              clearable: true,
              options: [
                { label: '全部状态', value: '' },
                ...extractionStatus
              ]
            },
            form: {
              type: 'select',
              options: extractionStatus,
              rules: [
                { required: true, message: '请选择萃取状态', trigger: 'change' }
              ]
            }
          },
          failureReason: {
            type: 'text',
            label: '失败原因',
            width: 200,
            showOverflowTooltip: true,
            search: { hidden: true },
            form: {
              type: 'textarea',
              props: {
                placeholder: '请输入失败原因',
                rows: 3
              }
            }
          },
          fileFormat: {
            type: 'text',
            label: '文件格式',
            width: 100,
            search: {
              type: 'select',
              placeholder: '请选择文件格式',
              clearable: true,
              options: [
                { label: '全部格式', value: '' },
                ...fileFormat
              ]
            },
            form: {
              type: 'select',
              options: fileFormat,
              rules: [
                { required: true, message: '请选择文件格式', trigger: 'change' }
              ]
            }
          },
          fileStatus: {
            type: 'text',
            label: '文件状态',
            width: 100,
            search: {
              type: 'select',
              placeholder: '请选择文件状态',
              clearable: true,
              options: [
                { label: '全部状态', value: '' },
                ...fileStatus
              ]
            },
            form: {
              type: 'select',
              options: fileStatus,
              rules: [
                { required: true, message: '请选择文件状态', trigger: 'change' }
              ]
            }
          }
        }
      }
    }
  },
  methods: {
    getExtractionStatusType(status) {
      const statusMap = {
        '进行中': 'warning',
        '成功': 'success',
        '失败': 'danger',
        '待处理': 'info'
      }
      return statusMap[status] || 'info'
    },
    getFileStatusType(status) {
      const statusMap = {
        '有效': 'success',
        '已删除': 'info',
        '损坏': 'danger',
        '待归档': 'warning'
      }
      return statusMap[status] || 'info'
    },
    handleCreateTask() {
      this.$refs.sheetRef.handleAdd()
    },
    handleReExtract() {
      // 重新萃取逻辑
      this.$message.success('重新萃取任务已提交')
      this.handleRefresh()
    },
    handleRefresh() {
      this.$refs.sheetRef.getTableData()
    },
    handleViewDetails(row) {
      this.$refs.sheetRef.handleInfo(row)
    },
    handleEdit(row) {
      this.$refs.sheetRef.handleEdit(row)
    },
    handleDelete(row) {
      this.$refs.sheetRef.handleRemove([row.id])
    }
  }
}
</script>

<style scoped>
.page-main {
  height: 100%;
}
</style>
